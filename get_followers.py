#!/usr/bin/env python3
"""
Instagram Followers Scraper
Fetches followers from a specified Instagram account and saves them to accounts-block.txt
"""

import http.client
import json
import urllib.parse
import sys
from typing import List, Dict, Any


def get_instagram_followers(username: str, amount: int = 50) -> List[Dict[str, Any]]:
    """
    Fetch followers from Instagram using RapidAPI
    
    Args:
        username: Instagram username to scrape followers from
        amount: Number of followers to fetch (default: 50)
    
    Returns:
        List of follower data dictionaries
    """
    conn = http.client.HTTPSConnection("instagram-scraper-stable-api.p.rapidapi.com")
    
    # Construct the Instagram URL
    instagram_url = f"https://www.instagram.com/{username}/"
    
    # Prepare payload
    payload_data = {
        'username_or_url': instagram_url,
        'data': 'followers',
        'amount': str(amount)
    }
    payload = urllib.parse.urlencode(payload_data)
    
    headers = {
        'x-rapidapi-key': "**************************************************",
        'x-rapidapi-host': "instagram-scraper-stable-api.p.rapidapi.com",
        'Content-Type': "application/x-www-form-urlencoded"
    }
    
    try:
        print(f"Fetching followers for @{username}...")
        conn.request("POST", "/get_ig_user_followers_v2.php", payload, headers)
        
        res = conn.getresponse()
        data = res.read()
        
        if res.status != 200:
            print(f"Error: HTTP {res.status} - {res.reason}")
            return []
        
        # Parse JSON response
        response_text = data.decode("utf-8")
        print(f"Raw response: {response_text[:200]}...")  # Show first 200 chars for debugging
        
        try:
            json_data = json.loads(response_text)
            return json_data
        except json.JSONDecodeError as e:
            print(f"Error parsing JSON: {e}")
            print(f"Response was: {response_text}")
            return []
            
    except Exception as e:
        print(f"Error making request: {e}")
        return []
    finally:
        conn.close()


def extract_usernames(followers_data: List[Dict[str, Any]]) -> List[str]:
    """
    Extract usernames from followers data
    
    Args:
        followers_data: List of follower data dictionaries
    
    Returns:
        List of usernames
    """
    usernames = []
    
    # Handle different possible response formats
    if isinstance(followers_data, dict):
        # If response is a dict, look for followers in common keys
        if 'followers' in followers_data:
            followers_data = followers_data['followers']
        elif 'data' in followers_data:
            followers_data = followers_data['data']
        elif 'users' in followers_data:
            followers_data = followers_data['users']
    
    if isinstance(followers_data, list):
        for follower in followers_data:
            if isinstance(follower, dict):
                # Try different possible username keys
                username = (follower.get('username') or 
                           follower.get('user') or 
                           follower.get('handle') or 
                           follower.get('screen_name'))
                if username:
                    usernames.append(username)
            elif isinstance(follower, str):
                # If follower is just a string (username)
                usernames.append(follower)
    
    return usernames


def save_to_accounts_block(usernames: List[str], filename: str = "accounts-block.txt") -> None:
    """
    Save usernames to accounts-block.txt file
    
    Args:
        usernames: List of usernames to save
        filename: Output filename (default: accounts-block.txt)
    """
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            for username in usernames:
                f.write(f"{username}\n")
        
        print(f"Successfully saved {len(usernames)} usernames to {filename}")
        
    except Exception as e:
        print(f"Error saving to file: {e}")


def main():
    """Main function"""
    target_username = "aynickmorin"
    amount_to_fetch = 100  # Adjust as needed
    
    print(f"Starting follower scraping for @{target_username}")
    print(f"Requesting {amount_to_fetch} followers...")
    
    # Fetch followers data
    followers_data = get_instagram_followers(target_username, amount_to_fetch)
    
    if not followers_data:
        print("No followers data received. Exiting.")
        sys.exit(1)
    
    # Extract usernames
    usernames = extract_usernames(followers_data)
    
    if not usernames:
        print("No usernames could be extracted from the response.")
        print("This might be due to API response format changes or account privacy settings.")
        sys.exit(1)
    
    print(f"Extracted {len(usernames)} usernames")
    
    # Save to file
    save_to_accounts_block(usernames)
    
    print("Done!")


if __name__ == "__main__":
    main()
